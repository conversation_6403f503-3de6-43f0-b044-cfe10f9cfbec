# Dify插件签名配置指南

## 已完成的步骤

✅ **密钥对生成**：已生成 `bilibili_plugin_key.private.pem` 和 `bilibili_plugin_key.public.pem`
✅ **插件签名**：已生成签名版本 `dify-plugin-Bilibili-Search-main.signed.difypkg`
✅ **签名验证**：签名验证成功

## 配置Dify环境以支持第三方签名验证

### 方案1：直接安装签名插件（推荐）

如果您的Dify环境已禁用签名验证（`FORCE_VERIFYING_SIGNATURE=false`），可以直接安装签名版本的插件：

```bash
# 使用签名版本的插件文件
dify-plugin-Bilibili-Search-main.signed.difypkg
```

### 方案2：启用第三方签名验证

如果您想启用第三方签名验证，请按以下步骤操作：

#### 1. 创建公钥目录

在您的Dify Docker环境中：

```bash
mkdir -p docker/volumes/plugin_daemon/public_keys
```

#### 2. 复制公钥文件

```bash
cp bilibili_plugin_key.public.pem docker/volumes/plugin_daemon/public_keys/
```

#### 3. 创建Docker Compose覆盖文件

创建 `docker-compose.override.yaml` 文件：

```yaml
services:
  plugin_daemon:
    environment:
      FORCE_VERIFYING_SIGNATURE: true
      THIRD_PARTY_SIGNATURE_VERIFICATION_ENABLED: true
      THIRD_PARTY_SIGNATURE_VERIFICATION_PUBLIC_KEYS: /app/storage/public_keys/bilibili_plugin_key.public.pem
```

#### 4. 重启Dify服务

```bash
cd docker
docker compose down
docker compose up -d
```

## 文件说明

- `bilibili_plugin_key.private.pem` - 私钥文件（请妥善保管，不要泄露）
- `bilibili_plugin_key.public.pem` - 公钥文件（用于验证签名）
- `dify-plugin-Bilibili-Search-main.difypkg` - 原始插件包
- `dify-plugin-Bilibili-Search-main.signed.difypkg` - 已签名的插件包

## 安全提醒

⚠️ **重要**：
- 私钥文件必须妥善保管，不要分享给他人
- 如果私钥泄露，攻击者可以为任何插件添加有效签名
- 建议在生产环境中使用第三方签名验证
- 公钥文件可以安全地分享给需要验证插件的管理员

## 使用说明

1. **开发环境**：可以直接使用签名版本的插件，无需额外配置
2. **生产环境**：建议启用第三方签名验证，确保只有可信的插件能够安装
3. **分发插件**：将 `.signed.difypkg` 文件和 `.public.pem` 文件一起提供给用户
