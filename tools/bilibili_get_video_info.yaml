identity:
  name: bilibili_get_video_info
  author: jingfelix
  label:
    en_US: Get Bilibili Video Info
    zh_Hans: 获取哔哩哔哩视频信息
    pt_BR: Obter informações do vídeo Bilibili
description:
  human:
    en_US: Get video information from Bilibili
    zh_Hans: 获取哔哩哔哩视频信息
    pt_BR: Obter informações do vídeo Bilibili
  llm: Get video information from Bilibili platform. Returns video details including titles, authors, descriptions and other metadata.
parameters:
  - name: bvid
    type: string
    required: true
    label:
      en_US: Bilibili Video bvid
      zh_Hans: 视频 bvid
      pt_BR: bvid do Vídeo Bilibili
    human_description:
      en_US: Enter the bvid of the video you want to get information for on Bilibili
      zh_Hans: 输入您想获取信息的哔哩哔哩视频的 bvid
      pt_BR: Digite o bvid do vídeo que você deseja obter informações no Bilibili
    llm_description: The bvid to get information for on Bilibili platform. This is the unique identifier for videos.
    form: llm
extra:
  python:
    source: tools/bilibili_get_video_info.py
