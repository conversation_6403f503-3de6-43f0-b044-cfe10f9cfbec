identity:
  name: bilibili_get_video_info
  author: jingfelix
  label:
    en_US: Get Bilibili Video Info
    zh_Hans: 获取哔哩哔哩视频信息
    pt_BR: Obter informações do vídeo Bilibili
description:
  human:
    en_US: Get video information from Bilibili
    zh_Hans: 获取哔哩哔哩视频信息
    pt_BR: Obter informações do vídeo Bilibili
  llm: Get video information from Bilibili platform. Returns video details including titles, authors, descriptions and other metadata.
parameters:
  - name: bvid
    type: string
    required: true
    label:
      en_US: Bilibili Video bvid
      zh_Hans: 视频 bvid
      pt_BR: bvid do Vídeo Bilibili
    human_description:
      en_US: Enter the bvid of the video you want to get information for on Bilibili
      zh_Hans: 输入您想获取信息的哔哩哔哩视频的 bvid
      pt_BR: Digite o bvid do vídeo que você deseja obter informações no Bilibili
    llm_description: The bvid to get information for on Bilibili platform. This is the unique identifier for videos.
    form: llm
output_schema:
  type: object
  properties:
    bvid:
      type: string
      description: Bilibili video ID
    tname:
      type: string
      description: Video category name
    tname_v2:
      type: string
      description: Video category name v2
    pic:
      type: string
      description: Video thumbnail URL
    title:
      type: string
      description: Video title
    pubdate:
      type: number
      description: Publication date timestamp
    ctime:
      type: number
      description: Creation time timestamp
    desc:
      type: string
      description: Video description
    owner:
      type: object
      description: Video owner information
      properties:
        mid:
          type: number
          description: User ID
        name:
          type: string
          description: Username
        face:
          type: string
          description: User avatar URL
    stat:
      type: object
      description: Video statistics
      properties:
        aid:
          type: number
          description: Archive ID
        view:
          type: number
          description: View count
        danmaku:
          type: number
          description: Danmaku count
        reply:
          type: number
          description: Reply count
        favorite:
          type: number
          description: Favorite count
        coin:
          type: number
          description: Coin count
        share:
          type: number
          description: Share count
        now_rank:
          type: number
          description: Current rank
        his_rank:
          type: number
          description: Historical rank
        like:
          type: number
          description: Like count
        dislike:
          type: number
          description: Dislike count
        evaluation:
          type: string
          description: Evaluation
        vt:
          type: number
          description: VT value
extra:
  python:
    source: tools/bilibili_get_video_info.py
