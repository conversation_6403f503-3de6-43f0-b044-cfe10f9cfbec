identity:
  name: bilibili_search
  author: jingfelix
  label:
    en_US: Bilibili Search
    zh_Hans: 哔哩哔哩搜索
    pt_BR: Pesquisa Bilibili
description:
  human:
    en_US: Search for videos on Bilibili
    zh_Hans: 在哔哩哔哩上搜索视频
    pt_BR: Pesquisar vídeos no Bilibili
  llm: Search for videos on Bilibili platform. Returns search results including titles, authors, descriptions and other metadata.
parameters:
  - name: keyword
    type: string
    required: true
    label:
      en_US: Search Keyword
      zh_Hans: 搜索关键词
      pt_BR: Palavra-chave
    human_description:
      en_US: Enter the keywords you want to search on Bilibili
      zh_Hans: 输入您想在哔哩哔哩上搜索的关键词
      pt_BR: Digite as palavras-chave que você deseja pesquisar no Bilibili
    llm_description: The keywords to search for on Bilibili platform. This can be video titles, usernames, or any other searchable content.
    form: llm
  - name: page
    type: number
    required: false
    label:
      en_US: Page Number
      zh_Hans: 页码
      pt_BR: <PERSON><PERSON><PERSON><PERSON>gina
    human_description:
      en_US: The page number of search results (default is 1)
      zh_Hans: 搜索结果的页码（默认为第1页）
      pt_BR: O número da página dos resultados da pesquisa (padrão é 1)
    llm_description: The page number of search results to retrieve. Each page contains multiple results. Defaults to 1 if not specified.
    form: llm
outputs:
  - name: seid
    type: string
    description: Search session ID
  - name: page
    type: number
    description: Current page number
  - name: pagesize
    type: number
    description: Number of results per page
  - name: numResults
    type: number
    description: Total number of search results
  - name: numPages
    type: number
    description: Total number of pages
  - name: result
    type: array
    description: Array of search results
    items:
      type: object
      properties:
        type:
          type: string
          description: Video type
        typename:
          type: string
          description: Video type name
        bvid:
          type: string
          description: Bilibili video ID
        title:
          type: string
          description: Video title
        description:
          type: string
          description: Video description
        pic:
          type: string
          description: Video thumbnail URL
        play:
          type: number
          description: Play count
        review:
          type: number
          description: Review count
        pubdate:
          type: number
          description: Publication date timestamp
        duration:
          type: string
          description: Video duration
        rank_score:
          type: number
          description: Ranking score (optional)
        like:
          type: number
          description: Like count
extra:
  python:
    source: tools/bilibili_search.py
