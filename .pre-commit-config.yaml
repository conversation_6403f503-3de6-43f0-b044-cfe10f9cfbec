# See https://pre-commit.com for more information
# See https://pre-commit.com/hooks.html for more hooks
repos:
-   repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v5.0.0
    hooks:
    -   id: trailing-whitespace
    -   id: end-of-file-fixer
    -   id: check-yaml
    -   id: check-added-large-files
-   repo: https://github.com/pycqa/isort
    rev: 5.13.2
    hooks:
    -   id: isort
        args: ["--profile", "black", "--filter-files"]
-   repo: https://github.com/python/black
    rev: 24.10.0
    hooks:
    -   id: black
-   repo: https://github.com/pdm-project/pdm
    rev: 2.22.1
    hooks:
    -   id: pdm-export
        args: ['--pyproject', '-o', 'requirements.txt', '--without-hashes']
        files: ^pdm.lock$
    -   id: pdm-lock-check
    -   id: pdm-sync
